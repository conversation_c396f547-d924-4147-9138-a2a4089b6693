<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏗️ Advanced Construction Cost Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="calculator-container">
        <div class="dark-mode-toggle" onclick="toggleDarkMode()" aria-label="Toggle Dark Mode">
            <span class="emoji moon">🌙</span>
            <span class="emoji sun">☀️</span>
        </div>

        <div class="input-section">
            <h1>🏗️ Advanced Construction Cost Calculator</h1>
            
            <div class="input-group">
                <label>📏 Plot Dimensions</label>
                <div class="dimension-inputs">
                    <input type="number" id="length" placeholder="Length" value="" min="0">
                    <input type="number" id="width" placeholder="Width" value="" min="0">
                    <select id="dimensionUnit">
                        <option value="ft">Feet</option>
                        <option value="yd">Yards</option>
                        <option value="in">Inches</option>
                        <option value="m">Meters</option>
                    </select>
                </div>
            </div>

            <div class="input-group">
                <label>🏢 Floors</label>
                <select id="floors">
                    <option value="1">Ground Floor</option>
                    <option value="2">First Floor</option>
                    <option value="3">Second Floor</option>
                    <option value="4">Third Floor</option>
                    <option value="5">Fourth Floor</option>
                </select>
            </div>

            <div class="input-group">
                <label>🏠 Floor Planning</label>
                <div class="floor-planning-box">
                    <div class="room-options-box">
                        <div class="room-search">
                            <input type="text" id="roomSearch" placeholder="🔍Search rooms...">
                        </div>
                        <div class="room-categories">
                            <div class="category">
                                <h4>🏡 Essential Rooms</h4>
                                <div class="room-options">
                                    <button class="room-option essential" data-room="bedroom" title="Sleeping area (200 sqft)">🛏️ <span>Bedroom</span>
                                        <button class="edit-room" onclick="editRoomSize('bedroom')">📏</button></button>
                                    <button class="room-option essential" data-room="bathroom" title="Bathing area (100 sqft)">🚿 <span>Bathroom</span>
                                        <button class="edit-room" onclick="editRoomSize('bathroom')">📏</button></button>
                                    <button class="room-option essential" data-room="kitchen" title="Cooking area (150 sqft)">🍳 <span>Kitchen</span>
                                        <button class="edit-room" onclick="editRoomSize('kitchen')">📏</button></button>
                                    <button class="room-option essential" data-room="living-room" title="Main living space (250 sqft)">🛋️ <span>Living Room</span>
                                        <button class="edit-room" onclick="editRoomSize('living-room')">📏</button></button>
                                    <button class="room-option essential" data-room="garage" title="Vehicle storage (300 sqft)">🚗 <span>Garage</span>
                                        <button class="edit-room" onclick="editRoomSize('garage')">📏</button></button>
                                    <button class="room-option essential" data-room="balcony" title="Outdoor space (50 sqft)">🌿 <span>Balcony</span>
                                        <button class="edit-room" onclick="editRoomSize('balcony')">📏</button></button>
                                    <button class="room-option essential" data-room="storage" title="Storage area (50 sqft)">🗄️ <span>Storage</span>
                                        <button class="edit-room" onclick="editRoomSize('storage')">📏</button></button>
                                    <button class="room-option essential" data-room="dining-area" title="Dining space (150 sqft)">🍽️ <span>Dining Area</span>
                                        <button class="edit-room" onclick="editRoomSize('dining-area')">📏</button></button>
                                    <button class="room-option essential" data-room="laundry-room" title="Laundry area (75 sqft)">🧺 <span>Laundry Room</span>
                                        <button class="edit-room" onclick="editRoomSize('laundry-room')">📏</button></button>
                                    <button class="room-option essential" data-room="home-office" title="Work space (120 sqft)">💼 <span>Home Office</span>
                                        <button class="edit-room" onclick="editRoomSize('home-office')">📏</button></button>
                                    <button class="room-option essential" data-room="mudroom" title="Entryway storage (80 sqft)">🥾 <span>Mudroom</span>
                                        <button class="edit-room" onclick="editRoomSize('mudroom')">📏</button></button>
                                    <button class="room-option essential" data-room="pantry" title="Food storage (60 sqft)">🍞 <span>Pantry</span>
                                        <button class="edit-room" onclick="editRoomSize('pantry')">📏</button></button>
                                    <button class="room-option essential" data-room="closet" title="Walk-in closet (90 sqft)">👔 <span>Walk-in Closet</span>
                                        <button class="edit-room" onclick="editRoomSize('closet')">📏</button></button>
                                    <button class="room-option essential" data-room="nursery" title="Baby room (150 sqft)">👶 <span>Nursery</span>
                                        <button class="edit-room" onclick="editRoomSize('nursery')">📏</button></button>
                                </div>
                            </div>
                            <div class="category">
                                <h4>🌟 Luxury Spaces</h4>
                                <div class="room-options">
                                    <button class="room-option luxury" data-room="study-room" title="Study and work area (100 sqft)">📚 <span>Study Room</span>
                                        <button class="edit-room" onclick="editRoomSize('study-room')">📏</button></button>
                                    <button class="room-option luxury" data-room="game-room" title="Entertainment space (200 sqft)">🎮 <span>Game Room</span>
                                        <button class="edit-room" onclick="editRoomSize('game-room')">📏</button></button>
                                    <button class="room-option luxury" data-room="meditation-room" title="Relaxation space (100 sqft)">🧘 <span>Meditation Room</span>
                                        <button class="edit-room" onclick="editRoomSize('meditation-room')">📏</button></button>
                                    <button class="room-option luxury" data-room="gym" title="Exercise area (150 sqft)">🏋️ <span>Gym</span>
                                        <button class="edit-room" onclick="editRoomSize('gym')">📏</button></button>
                                    <button class="room-option luxury" data-room="home-theater" title="Entertainment space (300 sqft)">🎥 <span>Home Theater</span>
                                        <button class="edit-room" onclick="editRoomSize('home-theater')">📏</button></button>
                                    <button class="room-option luxury" data-room="wine-cellar" title="Wine storage (150 sqft)">🍷 <span>Wine Cellar</span>
                                        <button class="edit-room" onclick="editRoomSize('wine-cellar')">📏</button></button>
                                    <button class="room-option luxury" data-room="library" title="Book collection space (180 sqft)">📖 <span>Library</span>
                                        <button class="edit-room" onclick="editRoomSize('library')">📏</button></button>
                                    <button class="room-option luxury" data-room="indoor-pool" title="Swimming area (400 sqft)">🏊 <span>Indoor Pool</span>
                                        <button class="edit-room" onclick="editRoomSize('indoor-pool')">📏</button></button>
                                    <button class="room-option luxury" data-room="spa-room" title="Wellness area (200 sqft)">💆 <span>Spa Room</span>
                                        <button class="edit-room" onclick="editRoomSize('spa-room')">📏</button></button>
                                    <button class="room-option luxury" data-room="bowling-alley" title="Recreation space (500 sqft)">🎳 <span>Bowling Alley</span>
                                        <button class="edit-room" onclick="editRoomSize('bowling-alley')">📏</button></button>
                                    <button class="room-option luxury" data-room="art-studio" title="Creative workspace (250 sqft)">🎨 <span>Art Studio</span>
                                        <button class="edit-room" onclick="editRoomSize('art-studio')">📏</button></button>
                                    <button class="room-option luxury" data-room="guest-house" title="Separate living quarters (800 sqft)">🏡 <span>Guest House</span>
                                        <button class="edit-room" onclick="editRoomSize('guest-house')">📏</button></button>
                                </div>
                            </div>
                            <div class="category">
                                <h4>🌱 Eco Features</h4>
                                <div class="room-options">
                                    <button class="room-option eco" data-room="solar-panels" title="Renewable energy system (100 sqft)">☀️ <span>Solar Panels</span>
                                        <button class="edit-room" onclick="editRoomSize('solar-panels')">📏</button></button>
                                    <button class="room-option eco" data-room="rainwater-harvesting" title="Water conservation system (50 sqft)">🌧️ <span>Rainwater Harvesting</span>
                                        <button class="edit-room" onclick="editRoomSize('rainwater-harvesting')">📏</button></button>
                                    <button class="room-option eco" data-room="energy-efficient-appliances" title="Energy-saving appliances (100 sqft)">⚡ <span>Energy-Efficient Appliances</span>
                                        <button class="edit-room" onclick="editRoomSize('energy-efficient-appliances')">📏</button></button>
                                    <button class="room-option eco" data-room="geothermal-heating" title="Earth-based heating (200 sqft)">🌍 <span>Geothermal Heating</span>
                                        <button class="edit-room" onclick="editRoomSize('geothermal-heating')">📏</button></button>
                                    <button class="room-option eco" data-room="green-roof" title="Vegetated roof system (250 sqft)">🌱 <span>Green Roof</span>
                                        <button class="edit-room" onclick="editRoomSize('green-roof')">📏</button></button>
                                    <button class="room-option eco" data-room="wind-turbine" title="Renewable energy system (150 sqft)">🌪️ <span>Wind Turbine</span>
                                        <button class="edit-room" onclick="editRoomSize('wind-turbine')">📏</button></button>
                                    <button class="room-option eco" data-room="composting" title="Waste management system (80 sqft)">♻️ <span>Composting System</span>
                                        <button class="edit-room" onclick="editRoomSize('composting')">📏</button></button>
                                    <button class="room-option eco" data-room="hydroponic-farm" title="Soil-less agriculture (400 sqft)">🌿 <span>Hydroponic Farm</span>
                                        <button class="edit-room" onclick="editRoomSize('hydroponic-farm')">📏</button></button>
                                    <button class="room-option eco" data-room="greywater-system" title="Water recycling (120 sqft)">💧 <span>Greywater System</span>
                                        <button class="edit-room" onclick="editRoomSize('greywater-system')">📏</button></button>
                                </div>
                            </div>
                            <div class="category">
                                <h4>🎯 Specialized Spaces</h4>
                                <div class="room-options">
                                    <button class="room-option specialized" data-room="panic-room" title="Secure emergency space (120 sqft)">🚨 <span>Panic Room</span>
                                        <button class="edit-room" onclick="editRoomSize('panic-room')">📏</button></button>
                                    <button class="room-option specialized" data-room="recording-studio" title="Soundproof music space (300 sqft)">🎙️ <span>Recording Studio</span>
                                        <button class="edit-room" onclick="editRoomSize('recording-studio')">📏</button></button>
                                    <button class="room-option specialized" data-room="darkroom" title="Photography development (100 sqft)">🎞️ <span>Darkroom</span>
                                        <button class="edit-room" onclick="editRoomSize('darkroom')">📏</button></button>
                                    <button class="room-option specialized" data-room="sauna" title="Steam relaxation (80 sqft)">🧖 <span>Sauna</span>
                                        <button class="edit-room" onclick="editRoomSize('sauna')">📏</button></button>
                                </div>
                            </div>
                            <div class="category">
                                <h4>🌍 Cultural Spaces</h4>
                                <div class="room-options">
                                    <button class="room-option cultural" data-room="chapel" title="Small worship area (200 sqft)">⛪ <span>Chapel</span>
                                        <button class="edit-room" onclick="editRoomSize('chapel')">📏</button></button>
                                    <button class="room-option cultural" data-room="tea-ceremony-room" title="Traditional tea space (150 sqft)">🍵 <span>Tea Ceremony Room</span>
                                        <button class="edit-room" onclick="editRoomSize('tea-ceremony-room')">📏</button></button>
                                    <button class="room-option cultural" data-room="yurt" title="Circular living space (300 sqft)">⛺ <span>Yurt</span>
                                        <button class="edit-room" onclick="editRoomSize('yurt')">📏</button></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="floor-plan-grid-box">
                        <h3>Floor Plan Grid 
                            <span class="tooltip">(Each cell represents a room)</span></h3>
                        <div id="areaWarning" class="warning">
                            ⚠️ Exceeding available plot area by <span id="excessArea">0</span>!
                        </div>
                        <div class="floor-plan-grid" id="floorPlanGrid"></div>
                    </div>
                </div>
            </div>

            <div class="input-group">
                <label>🏗️ Construction Quality</label>
                <div class="quality-buttons">
                    <button class="quality-btn" data-quality="1">Standard 🏠</button>
                    <button class="quality-btn" data-quality="2">Premium 🏡</button>
                    <button class="quality-btn" data-quality="3">Luxury 🏰</button>
                </div>
            </div>

            <div class="input-group">
                <label>📍 City</label>
                <select id="city">
                    <option value="delhi">Delhi NCR</option>
                    <option value="mumbai">Mumbai</option>
                    <option value="bangalore">Bangalore</option>
                    <option value="hyderabad">Hyderabad</option>
                    <option value="chandigarh">Chandigarh</option>
                </select>
            </div>

            <div class="input-group">
                <label>💱 Currency</label>
                <select id="currency">
                    <option value="INR">Indian Rupee (₹)</option>
                    <option value="USD">US Dollar ($)</option>
                    <option value="EUR">Euro (€)</option>
                    <option value="GBP">British Pound (£)</option>
                </select>
            </div>

            <button class="calculate-btn" onclick="calculateCost()">Calculate Now 🧮</button>
        </div>

        <div class="results-section">
            <div class="breakdown-card total-cost-section">
                <h2>💰 Total Estimated Cost Range</h2>
                <div class="total-estimate" id="totalEstimatedCost"></div>
            </div>

            <div class="breakdown-card time-section">
                <h2>⏳ Estimated Construction Time</h2>
                <div class="time-estimate" id="timeEstimate"></div>
            </div>

            <div class="breakdown-card space-section">
                <h2>📐 Space Utilization</h2>
                <div class="space-utilization" id="spaceDetails"></div>
                <div class="net-carpet-area" id="netCarpetArea"></div>
                <div class="chart-container">
                    <canvas id="spaceChart"></canvas>
                </div>
            </div>

            <div class="breakdown-card material-section">
                <h2>💰 Material Cost Breakdown</h2>
                <div id="materialTable"></div>
                <div class="total-cost" id="totalMaterialCost"></div>
                <div class="chart-container">
                    <canvas id="materialChart"></canvas>
                </div>
            </div>

            <div class="breakdown-card labor-section">
                <h2>👷 Labor Cost Breakdown</h2>
                <div id="laborTable"></div>
                <div class="total-cost" id="totalLaborCost"></div>
                <div class="chart-container">
                    <canvas id="laborChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>