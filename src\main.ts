import './styles/main.css';
import { Calculator } from './components/Calculator';
import { RegionService } from './config/regions';
import { initializePerformanceMonitoring } from './utils/performance';
import { securityManager } from './utils/security';
import { accessibilityManager } from './utils/accessibility';
import initI18n from './i18n';
import type { CalculatorConfig } from './types/calculator';

class App {
  private calculator: Calculator | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      // Show loading screen
      this.showLoadingScreen();

      // Initialize core services
      await this.initializeServices();

      // Initialize UI
      await this.initializeUI();

      // Initialize calculator
      await this.initializeCalculator();

      // Setup global event listeners
      this.setupGlobalEventListeners();

      // Hide loading screen
      this.hideLoadingScreen();

      this.isInitialized = true;
      console.log('🏗️ Advanced Construction Calculator initialized successfully');

    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.showErrorScreen(error as Error);
    }
  }

  private showLoadingScreen(): void {
    document.body.innerHTML = `
      <div class="loading-screen" id="loadingScreen">
        <div class="loading-container">
          <div class="loading-logo">
            <span class="logo-icon">🏗️</span>
            <h1 class="logo-text">Construction Calculator</h1>
          </div>
          <div class="loading-spinner">
            <div class="spinner"></div>
          </div>
          <p class="loading-message" id="loadingMessage">Initializing application...</p>
          <div class="loading-progress">
            <div class="progress-bar" id="progressBar"></div>
          </div>
        </div>
      </div>
    `;
  }

  private updateLoadingProgress(message: string, progress: number): void {
    const messageEl = document.getElementById('loadingMessage');
    const progressBar = document.getElementById('progressBar');
    
    if (messageEl) messageEl.textContent = message;
    if (progressBar) progressBar.style.width = `${progress}%`;
  }

  private async initializeServices(): Promise<void> {
    this.updateLoadingProgress('Initializing security...', 10);
    // Security manager is already initialized via import

    this.updateLoadingProgress('Setting up accessibility...', 20);
    // Accessibility manager is already initialized via import
    accessibilityManager.addSkipLink('main-content', 'Skip to main content');
    accessibilityManager.enhanceColorContrast();
    accessibilityManager.addFontSizeControls();

    this.updateLoadingProgress('Loading translations...', 30);
    await initI18n();

    this.updateLoadingProgress('Detecting region...', 40);
    await RegionService.initialize();

    this.updateLoadingProgress('Starting performance monitoring...', 50);
    initializePerformanceMonitoring();
  }

  private async initializeUI(): Promise<void> {
    this.updateLoadingProgress('Setting up user interface...', 60);
    
    // Create main application structure
    const appHTML = `
      <div class="app" id="app">
        <div class="app-container">
          <div id="calculator-container" class="calculator-container"></div>
        </div>
        
        <!-- PWA Install Prompt -->
        <div class="pwa-install-prompt" id="pwaInstallPrompt" style="display: none;">
          <div class="prompt-content">
            <span class="prompt-icon">📱</span>
            <span class="prompt-text">Install this app for a better experience</span>
            <button class="prompt-install-btn" id="pwaInstallBtn">Install</button>
            <button class="prompt-close-btn" id="pwaCloseBtn">×</button>
          </div>
        </div>
        
        <!-- Offline Indicator -->
        <div class="offline-indicator" id="offlineIndicator" style="display: none;">
          <span class="offline-icon">📡</span>
          <span class="offline-text">You're offline. Some features may be limited.</span>
        </div>
        
        <!-- Update Available Notification -->
        <div class="update-notification" id="updateNotification" style="display: none;">
          <div class="notification-content">
            <span class="notification-icon">🔄</span>
            <span class="notification-text">A new version is available!</span>
            <button class="notification-btn" id="updateBtn">Update</button>
            <button class="notification-close" id="updateCloseBtn">×</button>
          </div>
        </div>
      </div>
    `;

    // Replace loading screen with app
    document.body.innerHTML = appHTML;
  }

  private async initializeCalculator(): Promise<void> {
    this.updateLoadingProgress('Loading calculator...', 80);

    // Get default configuration
    const defaultConfig: CalculatorConfig = {
      city: 'delhi',
      currency: 'INR',
      quality: 'standard',
      dimensions: {
        length: 30,
        width: 40,
        floors: 1,
        unit: 'ft'
      },
      rooms: []
    };

    // Try to load saved configuration
    const savedConfig = this.loadSavedConfiguration();
    const config = savedConfig || defaultConfig;

    this.updateLoadingProgress('Initializing calculator...', 90);

    // Initialize calculator
    const container = document.getElementById('calculator-container') as HTMLElement;
    this.calculator = new Calculator(container, config);

    this.updateLoadingProgress('Ready!', 100);
  }

  private loadSavedConfiguration(): CalculatorConfig | null {
    try {
      const saved = securityManager.secureRetrieve('calculator_config');
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn('Failed to load saved configuration:', error);
      return null;
    }
  }

  private setupGlobalEventListeners(): void {
    // PWA Installation
    this.setupPWAInstallation();

    // Offline/Online detection
    this.setupOfflineDetection();

    // Service Worker updates
    this.setupServiceWorkerUpdates();

    // Keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Error handling
    this.setupErrorHandling();

    // Beforeunload handler
    this.setupBeforeUnload();
  }

  private setupPWAInstallation(): void {
    let deferredPrompt: any;

    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      deferredPrompt = e;
      
      const installPrompt = document.getElementById('pwaInstallPrompt');
      if (installPrompt) {
        installPrompt.style.display = 'block';
      }
    });

    const installBtn = document.getElementById('pwaInstallBtn');
    const closeBtn = document.getElementById('pwaCloseBtn');
    const prompt = document.getElementById('pwaInstallPrompt');

    installBtn?.addEventListener('click', async () => {
      if (deferredPrompt) {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        if (outcome === 'accepted') {
          console.log('PWA installed');
        }
        
        deferredPrompt = null;
        if (prompt) prompt.style.display = 'none';
      }
    });

    closeBtn?.addEventListener('click', () => {
      if (prompt) prompt.style.display = 'none';
    });
  }

  private setupOfflineDetection(): void {
    const offlineIndicator = document.getElementById('offlineIndicator');
    
    const updateOnlineStatus = () => {
      if (offlineIndicator) {
        offlineIndicator.style.display = navigator.onLine ? 'none' : 'block';
      }
      
      if (!navigator.onLine) {
        accessibilityManager.announce('You are now offline. Some features may be limited.', 'assertive');
      } else {
        accessibilityManager.announce('You are back online.', 'polite');
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    // Initial check
    updateOnlineStatus();
  }

  private setupServiceWorkerUpdates(): void {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        const updateNotification = document.getElementById('updateNotification');
        if (updateNotification) {
          updateNotification.style.display = 'block';
        }
      });

      const updateBtn = document.getElementById('updateBtn');
      const closeBtn = document.getElementById('updateCloseBtn');
      const notification = document.getElementById('updateNotification');

      updateBtn?.addEventListener('click', () => {
        window.location.reload();
      });

      closeBtn?.addEventListener('click', () => {
        if (notification) notification.style.display = 'none';
      });
    }
  }

  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (e) => {
      // Only handle shortcuts when not in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '/':
            e.preventDefault();
            // Focus search if available
            const searchInput = document.querySelector('input[type="search"], input[placeholder*="search"]') as HTMLInputElement;
            searchInput?.focus();
            break;
          
          case 'k':
            e.preventDefault();
            // Open command palette (future feature)
            console.log('Command palette shortcut');
            break;
            
          case ',':
            e.preventDefault();
            // Open settings (future feature)
            console.log('Settings shortcut');
            break;
        }
      }

      // Accessibility shortcuts
      switch (e.key) {
        case 'F1':
          e.preventDefault();
          // Show help (future feature)
          console.log('Help shortcut');
          break;
          
        case 'Escape':
          // Close any open modals or dropdowns
          document.querySelectorAll('[aria-expanded="true"]').forEach(element => {
            element.setAttribute('aria-expanded', 'false');
          });
          break;
      }
    });
  }

  private setupErrorHandling(): void {
    window.addEventListener('error', (e) => {
      console.error('Global error:', e.error);
      securityManager.logSecurityEvent('javascript_error', {
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno
      });
    });

    window.addEventListener('unhandledrejection', (e) => {
      console.error('Unhandled promise rejection:', e.reason);
      securityManager.logSecurityEvent('unhandled_rejection', {
        reason: e.reason
      });
    });
  }

  private setupBeforeUnload(): void {
    window.addEventListener('beforeunload', (e) => {
      // Save current state
      if (this.calculator) {
        const config = this.calculator.getConfig();
        securityManager.secureStore('calculator_config', JSON.stringify(config));
      }

      // Cleanup
      securityManager.cleanup();
      accessibilityManager.cleanup();
    });
  }

  private hideLoadingScreen(): void {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.remove();
      }, 300);
    }
  }

  private showErrorScreen(error: Error): void {
    document.body.innerHTML = `
      <div class="error-screen">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <h1 class="error-title">Something went wrong</h1>
          <p class="error-message">${error.message}</p>
          <button class="error-retry-btn" onclick="window.location.reload()">
            Try Again
          </button>
        </div>
      </div>
    `;
  }

  // Public methods
  public getCalculator(): Calculator | null {
    return this.calculator;
  }

  public isReady(): boolean {
    return this.isInitialized;
  }
}

// Initialize application
const app = new App();

// Start the application when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.initialize());
} else {
  app.initialize();
}

// Export for global access
(window as any).ConstructionCalculatorApp = app;

// Register service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', registration);
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  });
}
